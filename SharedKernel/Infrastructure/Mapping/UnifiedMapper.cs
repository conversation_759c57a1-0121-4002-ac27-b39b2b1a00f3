using System.Reflection;
using System.Linq.Expressions;
using SharedKernel.Abstractions.Mapping;

namespace SharedKernel.Infrastructure.Mapping;

/// <summary>
/// Univerzální mapper, který podporuje:
/// 1) automatické mapování vlastností se s<PERSON> (forward i reverse),
/// 2) konfigurovatelné mapování (forward i reverse),
/// 3) obousměrné aktualizace existujících instancí.
/// </summary>
/// <typeparam name="TSource">Zdrojov<PERSON> typ (obvykle DTO).</typeparam>
/// <typeparam name="TTarget">Cílový typ (obvykle Entity).</typeparam>
public class UnifiedMapper<TSource, TTarget> : IUnifiedMapper<TSource, TTarget>
    where TSource : class
    where TTarget : class
{
    // --- <PERSON><PERSON><PERSON> mappování ---
    private static readonly Lazy<Func<TSource, TTarget>> _autoForward =
        new(() => BuildDirectMapper<TSource, TTarget>(), LazyThreadSafetyMode.ExecutionAndPublication);
    private static readonly Lazy<Func<TTarget, TSource>> _autoReverse =
        new(() => BuildDirectMapper<TTarget, TSource>(), LazyThreadSafetyMode.ExecutionAndPublication);

    // --- Konfigurovatelné mapování ---
    private readonly Func<TSource, TTarget>? _configForward;
    private readonly Func<TTarget, TSource>? _configReverse;

    // --- Aktualizace existujících instancí ---
    private static readonly Lazy<Action<TSource, TTarget>> _updateForward =
        new(() => BuildUpdateMapper<TSource, TTarget>(), LazyThreadSafetyMode.ExecutionAndPublication);
    private static readonly Lazy<Action<TTarget, TSource>> _updateReverse =
        new(() => BuildUpdateMapper<TTarget, TSource>(), LazyThreadSafetyMode.ExecutionAndPublication);

    /// <summary>
    /// Inicializuje novou instanci UnifiedMapper s volitelnou konfigurací.
    /// </summary>
    /// <param name="forwardConfig">Konfigurace pro mapování TSource -> TTarget.</param>
    /// <param name="reverseConfig">Konfigurace pro mapování TTarget -> TSource.</param>
    public UnifiedMapper(
        Action<IMappingConfig<TSource, TTarget>>? forwardConfig = null,
        Action<IMappingConfig<TTarget, TSource>>? reverseConfig = null)
    {
        if (forwardConfig != null)
        {
            var fwd = new MappingConfig<TSource, TTarget>();
            forwardConfig.Invoke(fwd);
            _configForward = fwd.Compile();
        }

        if (reverseConfig != null)
        {
            var rev = new MappingConfig<TTarget, TSource>();
            reverseConfig.Invoke(rev);
            _configReverse = rev.Compile();
        }
    }

    // --- Public API ---
    
    /// <summary>
    /// Mapuje zdrojový objekt na cílový objekt.
    /// </summary>
    /// <param name="source">Zdrojový objekt.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Nová instance cílového typu.</returns>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je source null.</exception>
    public TTarget Map(TSource source, bool useConfig = false)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));



        var result = useConfig && _configForward != null ? _configForward(source) : _autoForward.Value(source);



        return result;
    }

    /// <summary>
    /// Mapuje cílový objekt zpět na zdrojový objekt.
    /// </summary>
    /// <param name="target">Cílový objekt.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Nová instance zdrojového typu.</returns>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je target null.</exception>
    public TSource MapBack(TTarget target, bool useConfig = false)
    {
        if (target == null) throw new ArgumentNullException(nameof(target));
        return useConfig && _configReverse != null ? _configReverse(target) : _autoReverse.Value(target);
    }

    /// <summary>
    /// Aktualizuje existující cílový objekt hodnotami ze zdrojového objektu.
    /// </summary>
    /// <param name="source">Zdrojový objekt.</param>
    /// <param name="target">Existující cílový objekt k aktualizaci.</param>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je source nebo target null.</exception>
    public void Update(TSource source, TTarget target)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (target == null) throw new ArgumentNullException(nameof(target));
        _updateForward.Value(source, target);
    }

    /// <summary>
    /// Aktualizuje existující zdrojový objekt hodnotami z cílového objektu.
    /// </summary>
    /// <param name="target">Cílový objekt.</param>
    /// <param name="source">Existující zdrojový objekt k aktualizaci.</param>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je source nebo target null.</exception>
    public void UpdateBack(TTarget target, TSource source)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (target == null) throw new ArgumentNullException(nameof(target));
        _updateReverse.Value(target, source);
    }

    /// <summary>
    /// Mapuje kolekci zdrojových objektů na kolekci cílových objektů.
    /// </summary>
    /// <param name="sources">Kolekce zdrojových objektů.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Kolekce cílových objektů.</returns>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je sources null.</exception>
    public IEnumerable<TTarget> MapCollection(IEnumerable<TSource> sources, bool useConfig = false)
    {
        if (sources == null) throw new ArgumentNullException(nameof(sources));
        return sources.Select(x => Map(x, useConfig));
    }

    /// <summary>
    /// Mapuje kolekci cílových objektů zpět na kolekci zdrojových objektů.
    /// </summary>
    /// <param name="targets">Kolekce cílových objektů.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Kolekce zdrojových objektů.</returns>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je targets null.</exception>
    public IEnumerable<TSource> MapBackCollection(IEnumerable<TTarget> targets, bool useConfig = false)
    {
        if (targets == null) throw new ArgumentNullException(nameof(targets));
        return targets.Select(x => MapBack(x, useConfig));
    }

    // --- Internals: builds direct mapper via Expressions ---

    /// <summary>
    /// Vytvoří mapovací funkci pomocí Expression Trees pro maximální výkon.
    /// </summary>
    private static Func<TIn, TOut> BuildDirectMapper<TIn, TOut>()
        where TIn : class
        where TOut : class
    {
        var sourceParam = Expression.Parameter(typeof(TIn), "source");
        var targetVar = Expression.Variable(typeof(TOut), "target");

        var assignments = new List<Expression>();

        // Vytvoření nové instance cílového typu
        var newTarget = Expression.New(typeof(TOut));
        var assignTarget = Expression.Assign(targetVar, newTarget);
        assignments.Add(assignTarget);

        foreach (var targetProp in typeof(TOut).GetProperties().Where(p => p.CanWrite))
        {
            // Speciální zpracování pro RowVersion při mapování na entity
            if (targetProp.Name == "RowVersion" && typeof(TOut).Name.EndsWith("Entity"))
            {
                // Nastavíme výchozí RowVersion pro nové entity
                var defaultRowVersion = Expression.Constant(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 });
                var assignRowVersion = Expression.Call(targetVar, targetProp.SetMethod!, defaultRowVersion);
                assignments.Add(assignRowVersion);
                continue;
            }

            var sourceProp = typeof(TIn).GetProperty(targetProp.Name, BindingFlags.Public | BindingFlags.Instance);
            if (sourceProp == null || !sourceProp.CanRead) continue;

            var sourceValue = Expression.Property(sourceParam, sourceProp);
            var convertedValue = targetProp.PropertyType != sourceProp.PropertyType
                ? Expression.Convert(sourceValue, targetProp.PropertyType)
                : (Expression)sourceValue;

            var assignExpr = Expression.Call(targetVar, targetProp.SetMethod!, convertedValue);
            assignments.Add(assignExpr);
        }

        assignments.Add(targetVar);
        var body = Expression.Block(new[] { targetVar }, assignments);
        return Expression.Lambda<Func<TIn, TOut>>(body, sourceParam).Compile();
    }

    /// <summary>
    /// Vytvoří aktualizační akci pomocí Expression Trees pro maximální výkon.
    /// </summary>
    private static Action<TIn, TOut> BuildUpdateMapper<TIn, TOut>()
        where TIn : class
        where TOut : class
    {
        var sourceParam = Expression.Parameter(typeof(TIn), "source");
        var targetParam = Expression.Parameter(typeof(TOut), "target");

        var assignments = new List<Expression>();

        foreach (var targetProp in typeof(TOut).GetProperties().Where(p => p.CanWrite))
        {
            // Přeskočíme RowVersion - ten se aktualizuje automaticky v ApplicationDbContext
            if (targetProp.Name == "RowVersion") continue;

            var sourceProp = typeof(TIn).GetProperty(targetProp.Name, BindingFlags.Public | BindingFlags.Instance);
            if (sourceProp == null || !sourceProp.CanRead) continue;

            var sourceValue = Expression.Property(sourceParam, sourceProp);
            var convertedValue = targetProp.PropertyType != sourceProp.PropertyType
                ? Expression.Convert(sourceValue, targetProp.PropertyType)
                : (Expression)sourceValue;

            var assignExpr = Expression.Call(targetParam, targetProp.SetMethod!, convertedValue);
            assignments.Add(assignExpr);
        }

        var body = Expression.Block(assignments);
        return Expression.Lambda<Action<TIn, TOut>>(body, sourceParam, targetParam).Compile();
    }
}

/// <summary>
/// Konfigurační pomocník pro vlastní mapování.
/// </summary>
/// <typeparam name="TSource">Zdrojový typ.</typeparam>
/// <typeparam name="TTarget">Cílový typ.</typeparam>
public class MappingConfig<TSource, TTarget> : IMappingConfig<TSource, TTarget>
    where TSource : class
    where TTarget : class
{
    /// <summary>
    /// Seznam mapovacích akcí, které budou aplikovány během mapování.
    /// </summary>
    private readonly List<Action<TSource, TTarget>> _mappings = new();

    /// <summary>
    /// Definuje vlastní mapování mezi vlastnostmi zdrojového a cílového typu.
    /// </summary>
    /// <typeparam name="TPropertySource">Typ vlastnosti zdrojového objektu.</typeparam>
    /// <typeparam name="TPropertyTarget">Typ vlastnosti cílového objektu.</typeparam>
    /// <param name="sourceExpression">Expression pro získání hodnoty ze zdrojového objektu.</param>
    /// <param name="targetExpression">Expression pro nastavení hodnoty v cílovém objektu.</param>
    /// <param name="converter">Volitelná konverzní funkce pro transformaci hodnoty.</param>
    public void Map<TPropertySource, TPropertyTarget>(
        Expression<Func<TSource, TPropertySource>> sourceExpression,
        Expression<Func<TTarget, TPropertyTarget>> targetExpression,
        Func<TPropertySource, TPropertyTarget>? converter = null)
    {
        // Kompiluje expressions do delegátů pro rychlé vykonání
        var sourceGetter = sourceExpression.Compile();

        // Pro setter musíme vytvořit expression, který nastaví hodnotu
        var memberExpr = (MemberExpression)targetExpression.Body;
        var targetParam = targetExpression.Parameters[0];
        var valueParam = Expression.Parameter(typeof(TPropertyTarget), "value");
        var assignExpr = Expression.Assign(memberExpr, valueParam);
        var setterLambda = Expression.Lambda<Action<TTarget, TPropertyTarget>>(assignExpr, targetParam, valueParam);
        var setter = setterLambda.Compile();

        // Přidává mapování s volitelnou konverzí
        _mappings.Add((source, target) =>
        {
            var sourceValue = sourceGetter(source);
            var targetValue = converter != null
                ? converter(sourceValue)
                : (TPropertyTarget)Convert.ChangeType(sourceValue, typeof(TPropertyTarget))!;
            setter(target, targetValue);
        });
    }

    /// <summary>
    /// Kompiluje nakonfigurovaná mapování do mapovací funkce.
    /// </summary>
    /// <returns>Funkce, která mapuje zdrojový objekt na cílový objekt pomocí nakonfigurovaných mapování.</returns>
    internal Func<TSource, TTarget> Compile()
    {
        return source =>
        {
            var target = (TTarget)Activator.CreateInstance(typeof(TTarget))!;
            foreach (var mapping in _mappings)
                mapping(source, target);
            return target;
        };
    }
}
