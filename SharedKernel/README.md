# SharedKernel

Tento projekt obsahuje obecné sdílené funkcionality, které mohou být použity napříč všemi vrstvami aplikace.

## Účel projektu

SharedKernel slouží jako centrální místo pro:

1. **Obecné modely** - Result pattern, str<PERSON><PERSON><PERSON><PERSON>, utility třídy
2. **Extension metody** - Rozšiřující metody pro běžné operace
3. **Konstanty a výčty** - Sdílené konstanty a enumerace
4. **Utility funkce** - Pomocné metody pro práci s daty
5. **Mapovací komponenty** - UnifiedMapper pro mapování mezi objekty
6. **Mediator komponenty** - Implementace návrhového vzoru Mediator pro komunikaci mezi komponentami
7. **Domain abstrakce** - Základní entity a domain events

## Architektonické zásady

SharedKernel je navržen podle následujících principů:

- **Nezávislost** - Nemá závislosti na jiných vrstvách aplikace
- **Obecnost** - Obsahuje pouze obecné funkcionality použitelné napříč projekty
- **Stabilita** - Změny v SharedKernel by měly být minimální a zpětně kompatibilní

## Struktura projektu

```
SharedKernel/
├── Abstractions/           # Rozhraní a abstrakce
│   ├── Mapping/           # Mapovací rozhraní (IUnifiedMapper, IMappingConfig)
│   └── Mediator/          # Mediator rozhraní (IMediator, IRequest, atd.)
├── Application/           # Aplikační abstrakce
├── Attributes/            # Atributy (Auditable, NotAudited)
├── Constants/             # Konstanty a výčty
├── Documentation/         # Dokumentace komponent
│   ├── Domain.md         # Detailní dokumentace Domain komponent
│   └── Mediator.md       # Detailní dokumentace Mediator komponenty
├── Domain/               # Domain abstrakce
│   ├── BaseEntity.cs     # Základní entita
│   ├── BaseTrackableEntity.cs  # Sledovatelná entita
│   ├── DomainEvent.cs    # Domain events
│   └── IEntity.cs        # Rozhraní entity
├── Infrastructure/       # Infrastrukturní implementace
│   ├── Mapping/          # Implementace mapování (UnifiedMapper, MappingConfig)
│   └── Mediator/        # Implementace Mediator komponenty
├── Models/              # Obecné modely (Result, PagedResult, atd.)
└── Utilities/           # Utility třídy a metody
```

## Použití

SharedKernel může být referencován ze všech vrstev aplikace:
- Domain
- Application  
- Infrastructure
- Presentation (DataCapture)

## Klíčové komponenty

### UnifiedMapper
Univerzální mapper pro mapování mezi různými typy objektů. Poskytuje:
- **Automatické mapování** - Mapování vlastností se stejnými názvy
- **Konfigurovatelné mapování** - Vlastní mapovací logika pomocí expressions
- **Obousměrné operace** - Mapování v obou směrech (TSource ↔ TTarget)
- **Aktualizace existujících instancí** - Bez vytváření nových objektů
- **Mapování kolekcí** - Hromadné operace
- **Optimalizace výkonu** - Použití Expression Trees a lazy loading

### Mediator
Implementace návrhového vzoru Mediator pro komunikaci mezi komponentami aplikace. Poskytuje:
- **Request/Response pattern** - Zpracování požadavků s odpověďmi
- **Notification pattern** - Publikování notifikací více handlerům
- **Pipeline behaviors** - Cross-cutting concerns (cache, validace, logování)

Detailní dokumentace: [Documentation/Mediator.md](Documentation/Mediator.md)

### Domain abstrakce
Základní třídy a rozhraní pro domain vrstvu podle principů Domain-Driven Design:
- **Entity rozhraní** - `IEntity`, `ITrackableEntity`, `ISoftDelete`
- **Základní entity** - `BaseEntity`, `BaseTrackableEntity`, `BaseSoftDeleteEntity`
- **Doménové události** - `DomainEvent`, `Event<T, TEnum>`
- **Audit trail** - Automatické sledování změn (CreatedAt, ModifiedAt, DeletedAt)
- **Soft delete** - Měkké mazání bez fyzického odstranění z databáze
- **Optimistické zamykání** - RowVersion pro ochranu před současnými změnami

Detailní dokumentace: [Documentation/Domain.md](Documentation/Domain.md)

### Result pattern
Implementace Result patternu pro elegantní zpracování chyb:
- `Result<T>` - Výsledek operace s hodnotou nebo chybou
- `PagedResult<T>` - Stránkovaný výsledek pro seznamy

## Závislosti

SharedKernel má minimální závislosti pouze na:
- .NET 9.0 runtime
- Microsoft.Extensions.DependencyInjection.Abstractions (pro Mediator)
- System.Text.Json (pro JSON konverze)
